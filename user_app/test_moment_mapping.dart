import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/user.dart';

void main() {
  // 模拟后端返回的数据，包含 isLiked 和 isBookmarked 字段
  final Map<String, dynamic> backendData = {
    'id': 1,
    'content': '测试动态',
    'isLiked': true,  // 后端返回的字段名
    'isBookmarked': false,  // 后端返回的字段名
    'like_count': 5,
    'comment_count': 3,
    'created_at': '2024-01-01T00:00:00Z',
    'publisher': {
      'id': 1,
      'name': '测试用户',
      'email': '<EMAIL>',
      'telephone_number': '13800138000',
    }
  };

  try {
    // 测试数据映射
    final moment = MomentVo.fromMap(backendData);
    
    print('映射测试结果:');
    print('moment.liked: ${moment.liked}');  // 应该是 true
    print('moment.bookmarked: ${moment.bookmarked}');  // 应该是 false
    print('moment.likeCount: ${moment.likeCount}');  // 应该是 5
    print('moment.commentCount: ${moment.commentCount}');  // 应该是 3
    
    if (moment.liked == true && moment.bookmarked == false) {
      print('✅ 数据映射成功！点赞状态正确映射');
    } else {
      print('❌ 数据映射失败！');
      print('期望: liked=true, bookmarked=false');
      print('实际: liked=${moment.liked}, bookmarked=${moment.bookmarked}');
    }
  } catch (e) {
    print('❌ 映射过程中发生错误: $e');
  }
}
